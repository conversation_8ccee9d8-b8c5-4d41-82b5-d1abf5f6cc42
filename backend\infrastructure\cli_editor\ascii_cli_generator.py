"""
ASCII CLI Generator Module
==========================

This module provides the AsciiCliGenerator mixin for generating ASCII CLI (Common Layer Interface) files
from layer data. It handles ASCII format generation compatible with recoater hardware.

Key Features:
- Generate ASCII CLI files for single layers or ranges.
- Hardware-compatible ASCII format (unlike binary format).
- Support for custom headers and layer count management.
- String-based formatting for polylines and hatch lines.

Dependencies:
- Relies on cli_models for data structures and cli_exceptions for error handling.

Usage:
    class MyClass(AsciiCliGenerator):
        pass
    instance = MyClass()
    cli_bytes = instance.generate_single_layer_ascii_cli(layer)
"""
from typing import List

from .cli_exceptions import CliGenerationError
from .cli_models import CliLayer


class AsciiCliGenerator:
    """Mixin for handling generation of ASCII CLI files from layer data."""

    def write_ascii_header(self, header_lines: List[str], lines: List[str], num_layers: int = 1) -> List[str]:
        """
        Writes ASCII CLI header lines to the output lines list.

        This method processes the provided header lines and ensures the correct layer count
        is included in the $$LAYERS/ directive. If no header lines are provided, it creates
        a minimal default header.

        Args:
            header_lines: List of header lines from the original CLI file, or None to use defaults
            lines: The output list to append header lines to
            num_layers: The number of layers in the CLI file (default: 1)

        Returns:
            The updated lines list with header information appended

        Raises:
            None
        """
        if header_lines is None:
                # Use a minimal header similar to the example file
            header_lines = [
                    "$$HEADERSTART",
                    "$$ASCII",
                    "$$UNITS/00000000.005000",
                    "$$VERSION/200",
                    "$$LABEL/1,default",
                    "$$DATE/060623",
                    "$$DIMENSION/-0000020.000000,-0000020.000000,00000000.080000,00000020.000000,00000020.000000,00000040.000000",
                    f"$$LAYERS/{num_layers:06d}",
                    "$$HEADEREND"
                ]

            # Add header lines, but update the layer count
        for line in header_lines:
            if line.startswith("$$LAYERS/"):
                    # Update the layer count to match the actual number of layers
                lines.append(f"$$LAYERS/{num_layers:06d}")
            else:
                lines.append(line)

            # Ensure we have $$GEOMETRYSTART after header
        if "$$GEOMETRYSTART" not in lines:
            lines.append("$$GEOMETRYSTART")

        return lines

    def write_ascii_polylines(self, layer: CliLayer, lines: List[str]) -> List[str]:
        """
        Writes ASCII CLI polyline data for a layer to the output lines list.

        This method converts all polylines in the given layer to ASCII CLI format
        and appends them to the provided lines list. Each polyline is formatted as:
        $$POLYLINE/part_id,direction,num_points,x1,y1,x2,y2,...

        Args:
            layer: The CliLayer object containing polylines to convert
            lines: The output list to append polyline data to

        Returns:
            The updated lines list with polyline data appended

        Raises:
            None
        """
        for poly in layer.polylines:
            if len(poly.points) > 0:
                # Format: $$POLYLINE/part_id,direction,num_points,x1,y1,x2,y2,...
                point_coords = []
                for point in poly.points:
                    point_coords.extend([f"{point.x:.5f}", f"{point.y:.5f}"])

                polyline_data = f"$$POLYLINE/{poly.part_id},{poly.direction},{len(poly.points)},{','.join(point_coords)}"
                lines.append(polyline_data)
        return lines

    def write_ascii_hatchlines(self, layer: CliLayer, lines: List[str]) -> List[str]:
        """
        Writes ASCII CLI hatch line data for a layer to the output lines list.

        This method converts all hatch lines in the given layer to ASCII CLI format
        and appends them to the provided lines list. Each hatch group is formatted as:
        $$HATCHES/group_id,num_lines,x1,y1,x2,y2,x3,y3,x4,y4,...

        Args:
            layer: The CliLayer object containing hatch lines to convert
            lines: The output list to append hatch data to

        Returns:
            The updated lines list with hatch data appended

        Raises:
            None
        """
        for hatch in layer.hatches:
            if len(hatch.lines) > 0:
                # Format: $$HATCHES/group_id,num_lines,x1,y1,x2,y2,x3,y3,x4,y4,...
                line_coords = []
                for line in hatch.lines:
                    line_coords.extend([
                            f"{line[0].x:.5f}", f"{line[0].y:.5f}",
                            f"{line[1].x:.5f}", f"{line[1].y:.5f}"
                        ])

                hatch_data = f"$$HATCHES/{hatch.group_id},{len(hatch.lines)},{','.join(line_coords)}"
                lines.append(hatch_data)    
        return lines    

    def generate_single_layer_ascii_cli(self, layer: CliLayer, header_lines: List[str] = None) -> bytes:
        """
        Generates ASCII CLI file data for a single layer.

        This method creates ASCII CLI format that is compatible with the actual hardware,
        unlike the binary format which doesn't work with the recoater hardware.

        Args:
            layer: The CliLayer object to convert to ASCII CLI format
            header_lines: Optional header lines to include (defaults to minimal header)

        Returns:
            A byte string containing the ASCII CLI file data for the single layer

        Raises:
            CliGenerationError: If the layer data cannot be serialized
        """
        try:
            lines = []

            # 1. Write Header
            lines = self.write_ascii_header(header_lines, lines, num_layers=1)

            # 2. Write Layer Command
            lines.append(f"$$LAYER/{layer.z_height}")

            # 3. Write Polylines & Hatches
            lines = self.write_ascii_polylines(layer, lines)
            lines = self.write_ascii_hatchlines(layer, lines)

            # Add geometry end
            lines.append("$$GEOMETRYEND")

            # Join all lines with newlines and encode to bytes
            cli_content = "\n".join(lines)
            return cli_content.encode('ascii')

        except Exception as e:
            raise CliGenerationError(f"Failed to generate ASCII CLI data for layer: {e}")

    def generate_ascii_cli_from_layer_range(self, layers: List[CliLayer], header_lines: List[str] = None, is_aligned: bool = False) -> bytes:
        """
        Generates a complete ASCII CLI file from a range of layers.

        This method creates ASCII CLI format that is compatible with the actual hardware,
        unlike the binary format which doesn't work with the recoater hardware.

        Args:
            layers: A list of CliLayer objects to include in the file.
            header_lines: Optional header lines. A minimal header is used if not provided.
            is_aligned: Whether the CLI data should be aligned format. Currently not used but kept for compatibility.

        Returns:
            A byte string containing the complete ASCII CLI file data.

        Raises:
            CliGenerationError: If the layer list is empty or data cannot be serialized.
        """
        if not layers:
            raise CliGenerationError("Cannot generate CLI data from an empty layer range")

        try:
            lines = []

            # 1. Write Header
            lines = self.write_ascii_header(header_lines, lines, num_layers=len(layers))

            # 2. Write each layer's data
            for layer in layers:
                # Write Layer Command
                lines.append(f"$$LAYER/{layer.z_height}")

                # Write Polylines & Hatchlines
                lines = self.write_ascii_polylines(layer, lines)
                lines = self.write_ascii_hatchlines(layer, lines)

            # Add geometry end
            lines.append("$$GEOMETRYEND")

            # Join all lines with newlines and encode to bytes
            cli_content = "\n".join(lines)
            return cli_content.encode('ascii')

        except Exception as e:
            raise CliGenerationError(f"Failed to generate ASCII CLI data for layer range: {e}")
